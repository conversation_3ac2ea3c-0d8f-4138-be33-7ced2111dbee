<template>
  <div class="flex flex-col h-full">
    <div class="flex flex-row gap-2 flex-wrap mt-2 mb-2">
      <UBadge
        color="neutral"
        variant="subtle"
        size="xs"
      >
        {{ $t(data.model_name) }}
      </UBadge>
      <UBadge
        v-if="!runtimeConfig.public.features.beta"
        color="warning"
        variant="subtle"
        size="xs"
      >
        {{ $t(data.used_credit || 0) }} {{ $t("Credits") }}
      </UBadge>
    </div>
    <div class="mt-auto grid grid-cols-1 sm:grid-cols-2 gap-2">
      <UButton
        icon="bxs:detail"
        color="neutral"
        variant="ghost"
        size="xs"
        class="text-white hover:text-gray-800 dark:hover:text-gray-50 bg-white/10 w-full justify-center"
        :label="$t('Detail')"
        @click.stop="openFullScreen"
      />
      <UButton
        icon="material-symbols:delete"
        color="neutral"
        variant="ghost"
        size="xs"
        class="text-white hover:text-gray-800 dark:hover:text-gray-50 bg-white/10 w-full justify-center"
        :label="$t('Delete')"
        @click.stop="handleDelete"
      />
      <UButton
        icon="mingcute:copy-fill"
        color="neutral"
        variant="ghost"
        size="xs"
        class="group/copy text-white hover:text-gray-800 dark:hover:text-gray-50 bg-white/10 w-full justify-center sm:col-span-2"
        @click.stop="copyToClipboard(data.uuid)"
      >
        <span class="group-hover/copy:hidden line-clamp-1 ">
          {{ shortenText(data.uuid, 30) }}
        </span>
        <span class="group-hover/copy:block hidden">
          {{ $t("Click to copy") }}
        </span>
      </UButton>
      <BaseDownloadButton
        :link="downloadLink"
        :label="$t('Download')"
        size="xs"
        block
        class="text-white hover:text-gray-800 dark:hover:text-gray-50 bg-white/10 w-full sm:col-span-2"
        variant="ghost"
      />
      <UButton
        v-if="showCloseButton && !isOpeningMenu"
        icon="material-symbols:close"
        color="neutral"
        variant="soft"
        size="xs"
        class="mt-auto text-white hover:text-gray-800 dark:hover:text-gray-50 bg-transparent w-full sm:col-span-2 justify-center"
        :label="$t('Close')"
        @click.stop="() => emits('close')"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n()
const historyStore = useHistoryStore()
const { openConfirm } = useConfirm()
const toast = useToast()
const router = useRouter()
const route = useRoute()
const runtimeConfig = useRuntimeConfig()
const { historyDetail, showDetailModal } = storeToRefs(historyStore)
const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
  showCloseButton: {
    type: Boolean,
    default: false
  },
  isOpeningMenu: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['close'])

const firstImage = computed(() => {
  return props.data?.generated_image?.[0] || {}
})

// Handle delete action
const handleDelete = () => {
  openConfirm({
    title: t('confirmDelete') || 'Confirm Delete',
    description:
      t('confirmDeleteDescription')
      || 'Are you sure you want to delete this item? This action cannot be undone.',
    icon: 'i-lucide-trash-2',
    confirmText: t('delete') || 'Delete',
    cancelText: t('cancel') || 'Cancel',
    onConfirm: async () => {
      const result = await historyStore.deleteHistory(props.data.id)
      if (result !== null) {
        toast.add({
          title: t('success.deleted') || 'Success',
          description:
            t('historyDeleted') || 'History item deleted successfully',
          color: 'success'
        })
      } else {
        toast.add({
          title: t('error.general') || 'Error',
          description:
            historyStore.errors.deleteHistory?.message
            || t('deleteError')
            || 'Failed to delete history item',
          color: 'error'
        })
        // Throw error to prevent modal from closing
        throw new Error('Delete failed')
      }
    }
  })
}

const openFullScreen = () => {
  historyDetail.value = props.data as any
  showDetailModal.value = true
  // Update the URL to include the ID for navigation
  if (props.data.uuid) {
    router.push({ query: { ...route.query, uuid: props.data.uuid } })
  }
}

const downloadLink = computed(() => {
  if (props.data.type === 'image') {
    return firstImage.value?.image_url
  }
  if (props.data.type === 'video') {
    return lastGenerated.value?.video_url
  }
  if (props.data.type === 'tts-text') {
    return lastGeneratedAudio.value?.audio_url
  }
  return ''
})

const lastGenerated = computed(() => {
  return (
    props.data?.generated_video?.[props.data?.generated_video?.length - 1] || {}
  )
})

const lastGeneratedAudio = computed(() => {
  return (
    props.data?.generated_audio?.[props.data?.generated_audio?.length - 1] || {}
  )
})
</script>
